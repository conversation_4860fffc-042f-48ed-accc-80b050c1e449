"""
配置控制中心 - FastAPI 服务端
提供 RESTful API 接口管理配置数据
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel
from datetime import datetime
from typing import Dict, Any, Optional
import json
import uvicorn

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./config_center.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据库模型
class ConfigModel(Base):
    __tablename__ = "configs"
    
    id = Column(Integer, primary_key=True, index=True)
    app_name = Column(String, index=True, nullable=False)
    config_key = Column(String, nullable=False)
    config_value = Column(Text, nullable=False)
    value_type = Column(String, nullable=False)  # 'string', 'int', 'float', 'bool', 'json'
    updated_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

class ConfigLog(Base):
    __tablename__ = "config_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    app_name = Column(String, nullable=False)
    config_key = Column(String, nullable=False)
    old_value = Column(Text)
    new_value = Column(Text, nullable=False)
    changed_at = Column(DateTime, default=datetime.utcnow)

# 创建数据库表
Base.metadata.create_all(bind=engine)

# Pydantic 模型
class ConfigUpdate(BaseModel):
    app: str
    configs: Dict[str, Any]

class ConfigResponse(BaseModel):
    app: str
    configs: Dict[str, Any]
    last_updated: Optional[datetime] = None

# FastAPI 应用
app = FastAPI(title="配置控制中心", description="本地配置管理系统", version="1.0.0")

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库依赖
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 辅助函数
def serialize_value(value: Any) -> tuple[str, str]:
    """将值序列化为字符串并返回类型"""
    if isinstance(value, bool):
        return str(value).lower(), "bool"
    elif isinstance(value, int):
        return str(value), "int"
    elif isinstance(value, float):
        return str(value), "float"
    elif isinstance(value, (dict, list)):
        return json.dumps(value), "json"
    else:
        return str(value), "string"

def deserialize_value(value_str: str, value_type: str) -> Any:
    """根据类型反序列化值"""
    if value_type == "bool":
        return value_str.lower() == "true"
    elif value_type == "int":
        return int(value_str)
    elif value_type == "float":
        return float(value_str)
    elif value_type == "json":
        return json.loads(value_str)
    else:
        return value_str

# API 路由
@app.get("/")
async def root():
    return {"message": "配置控制中心运行中", "version": "1.0.0"}

@app.get("/config")
async def get_config(app_name: str, db: Session = Depends(get_db)):
    """获取指定 app 的配置"""
    if not app_name:
        raise HTTPException(status_code=400, detail="app 参数不能为空")
    
    configs = db.query(ConfigModel).filter(
        ConfigModel.app_name == app_name,
        ConfigModel.is_active == True
    ).all()
    
    if not configs:
        # 如果没有配置，返回默认配置
        default_configs = {
            "feature_x_enabled": True,
            "max_threads": 5,
            "logging_level": "debug"
        }
        return {"app": app_name, "configs": default_configs, "last_updated": None}
    
    result_configs = {}
    last_updated = None
    
    for config in configs:
        result_configs[config.config_key] = deserialize_value(config.config_value, config.value_type)
        if last_updated is None or config.updated_at > last_updated:
            last_updated = config.updated_at
    
    return {"app": app_name, "configs": result_configs, "last_updated": last_updated}

@app.post("/config/update")
async def update_config(config_update: ConfigUpdate, db: Session = Depends(get_db)):
    """更新配置"""
    app_name = config_update.app
    configs = config_update.configs
    
    if not app_name:
        raise HTTPException(status_code=400, detail="app 参数不能为空")
    
    if not configs:
        raise HTTPException(status_code=400, detail="configs 不能为空")
    
    updated_keys = []
    
    for key, value in configs.items():
        # 查找现有配置
        existing_config = db.query(ConfigModel).filter(
            ConfigModel.app_name == app_name,
            ConfigModel.config_key == key,
            ConfigModel.is_active == True
        ).first()
        
        value_str, value_type = serialize_value(value)
        
        if existing_config:
            # 记录变更日志
            config_log = ConfigLog(
                app_name=app_name,
                config_key=key,
                old_value=existing_config.config_value,
                new_value=value_str
            )
            db.add(config_log)
            
            # 更新配置
            existing_config.config_value = value_str
            existing_config.value_type = value_type
            existing_config.updated_at = datetime.utcnow()
        else:
            # 创建新配置
            new_config = ConfigModel(
                app_name=app_name,
                config_key=key,
                config_value=value_str,
                value_type=value_type
            )
            db.add(new_config)
            
            # 记录变更日志
            config_log = ConfigLog(
                app_name=app_name,
                config_key=key,
                old_value=None,
                new_value=value_str
            )
            db.add(config_log)
        
        updated_keys.append(key)
    
    db.commit()
    
    return {
        "message": "配置更新成功",
        "app": app_name,
        "updated_keys": updated_keys,
        "timestamp": datetime.utcnow()
    }

@app.get("/config/status")
async def get_all_status(db: Session = Depends(get_db)):
    """获取所有 app 的配置状态"""
    configs = db.query(ConfigModel).filter(ConfigModel.is_active == True).all()
    
    apps_status = {}
    
    for config in configs:
        app_name = config.app_name
        if app_name not in apps_status:
            apps_status[app_name] = {
                "configs": {},
                "last_updated": config.updated_at
            }
        
        apps_status[app_name]["configs"][config.config_key] = deserialize_value(
            config.config_value, config.value_type
        )
        
        if config.updated_at > apps_status[app_name]["last_updated"]:
            apps_status[app_name]["last_updated"] = config.updated_at
    
    return {"apps": apps_status, "total_apps": len(apps_status)}

@app.get("/config/logs")
async def get_config_logs(app_name: str = None, limit: int = 50, db: Session = Depends(get_db)):
    """获取配置变更日志"""
    query = db.query(ConfigLog)
    
    if app_name:
        query = query.filter(ConfigLog.app_name == app_name)
    
    logs = query.order_by(ConfigLog.changed_at.desc()).limit(limit).all()
    
    return {
        "logs": [
            {
                "id": log.id,
                "app_name": log.app_name,
                "config_key": log.config_key,
                "old_value": log.old_value,
                "new_value": log.new_value,
                "changed_at": log.changed_at
            }
            for log in logs
        ]
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
