"""
Agent 使用示例
演示如何使用配置 Agent
"""

from main import ConfigAgent
import time
import threading

def example_1():
    """示例1: 基本使用"""
    print("=== 示例1: 基本使用 ===")
    
    agent = ConfigAgent("test_app")
    
    # 在单独的线程中运行 Agent
    agent_thread = threading.Thread(target=agent.start)
    agent_thread.daemon = True
    agent_thread.start()
    
    # 让 Agent 运行一段时间
    time.sleep(30)
    
    # 停止 Agent
    agent.stop()
    print("示例1 完成")

def example_2():
    """示例2: 自定义配置"""
    print("=== 示例2: 自定义配置 ===")
    
    agent = ConfigAgent("production_app", "http://localhost:8000")
    agent.fetch_interval = 5  # 5秒拉取一次
    
    # 获取当前状态
    status = agent.get_status()
    print(f"Agent 状态: {status}")
    
    # 手动获取一次配置
    config = agent.fetch_config()
    if config:
        print(f"获取到配置: {config}")
        agent.update_config(config)
    
    print("示例2 完成")

def example_3():
    """示例3: 监控配置变化"""
    print("=== 示例3: 监控配置变化 ===")
    
    agent = ConfigAgent("test_app")
    
    # 获取初始配置
    initial_config = agent.fetch_config()
    if initial_config:
        agent.update_config(initial_config)
        print(f"初始配置: {initial_config}")
    
    # 模拟配置变化监控
    for i in range(3):
        time.sleep(5)
        new_config = agent.fetch_config()
        if new_config:
            changed = agent.update_config(new_config)
            if changed:
                print(f"第 {i+1} 次检查: 配置已变化")
            else:
                print(f"第 {i+1} 次检查: 配置无变化")
    
    print("示例3 完成")

if __name__ == "__main__":
    print("配置 Agent 使用示例")
    print("请确保配置控制中心已启动 (cd controller && python main.py)")
    print()
    
    try:
        example_1()
        print()
        example_2()
        print()
        example_3()
    except KeyboardInterrupt:
        print("\n示例被中断")
    except Exception as e:
        print(f"示例运行出错: {e}")
