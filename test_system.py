#!/usr/bin/env python3
"""
系统测试脚本
测试配置控制系统的各个组件是否正常工作
"""

import requests
import json
import time
import sys
from pathlib import Path

class SystemTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_app = "test_system"
        
    def test_api_connection(self):
        """测试 API 连接"""
        print("🔗 测试 API 连接...")
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ API 连接正常")
                return True
            else:
                print(f"❌ API 连接失败，状态码: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到配置控制中心，请确保服务已启动")
            return False
        except Exception as e:
            print(f"❌ API 连接测试失败: {e}")
            return False
    
    def test_config_crud(self):
        """测试配置的增删改查"""
        print("📝 测试配置 CRUD 操作...")
        
        # 测试配置
        test_config = {
            "app": self.test_app,
            "configs": {
                "test_feature": True,
                "test_number": 42,
                "test_string": "hello",
                "test_float": 3.14,
                "test_array": ["a", "b", "c"],
                "test_object": {"key": "value"}
            }
        }
        
        # 创建配置
        try:
            response = requests.post(
                f"{self.base_url}/config/update",
                json=test_config,
                timeout=5
            )
            if response.status_code == 200:
                print("✅ 配置创建成功")
            else:
                print(f"❌ 配置创建失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 配置创建失败: {e}")
            return False
        
        # 读取配置
        try:
            response = requests.get(
                f"{self.base_url}/config",
                params={"app": self.test_app},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                configs = data.get("configs", {})
                
                # 验证配置内容
                expected = test_config["configs"]
                for key, expected_value in expected.items():
                    if key not in configs:
                        print(f"❌ 配置项 {key} 未找到")
                        return False
                    
                    actual_value = configs[key]
                    if actual_value != expected_value:
                        print(f"❌ 配置项 {key} 值不匹配: 期望 {expected_value}, 实际 {actual_value}")
                        return False
                
                print("✅ 配置读取和验证成功")
                return True
            else:
                print(f"❌ 配置读取失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 配置读取失败: {e}")
            return False
    
    def test_config_update(self):
        """测试配置更新"""
        print("🔄 测试配置更新...")
        
        # 更新配置
        updated_config = {
            "app": self.test_app,
            "configs": {
                "test_feature": False,  # 改变布尔值
                "test_number": 100,     # 改变数字
                "new_config": "added"   # 添加新配置
            }
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/config/update",
                json=updated_config,
                timeout=5
            )
            if response.status_code == 200:
                print("✅ 配置更新成功")
                
                # 验证更新
                response = requests.get(
                    f"{self.base_url}/config",
                    params={"app": self.test_app},
                    timeout=5
                )
                if response.status_code == 200:
                    data = response.json()
                    configs = data.get("configs", {})
                    
                    if configs.get("test_feature") == False and \
                       configs.get("test_number") == 100 and \
                       configs.get("new_config") == "added":
                        print("✅ 配置更新验证成功")
                        return True
                    else:
                        print("❌ 配置更新验证失败")
                        return False
                else:
                    print("❌ 配置更新验证失败")
                    return False
            else:
                print(f"❌ 配置更新失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 配置更新失败: {e}")
            return False
    
    def test_status_api(self):
        """测试状态 API"""
        print("📊 测试状态 API...")
        
        try:
            response = requests.get(f"{self.base_url}/config/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                apps = data.get("apps", {})
                
                if self.test_app in apps:
                    print("✅ 状态 API 测试成功")
                    return True
                else:
                    print("❌ 状态 API 中未找到测试应用")
                    return False
            else:
                print(f"❌ 状态 API 测试失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 状态 API 测试失败: {e}")
            return False
    
    def test_logs_api(self):
        """测试日志 API"""
        print("📋 测试日志 API...")
        
        try:
            response = requests.get(
                f"{self.base_url}/config/logs",
                params={"app_name": self.test_app, "limit": 10},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                logs = data.get("logs", [])
                
                if len(logs) > 0:
                    print(f"✅ 日志 API 测试成功，找到 {len(logs)} 条日志")
                    return True
                else:
                    print("⚠️ 日志 API 测试成功，但未找到日志记录")
                    return True
            else:
                print(f"❌ 日志 API 测试失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 日志 API 测试失败: {e}")
            return False
    
    def test_file_structure(self):
        """测试文件结构"""
        print("📁 测试文件结构...")
        
        required_files = [
            "controller/main.py",
            "controller/requirements.txt",
            "agent/main.py",
            "agent/requirements.txt",
            "web-client/package.json",
            "web-client/src/App.js",
            "README.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ 缺少文件: {', '.join(missing_files)}")
            return False
        else:
            print("✅ 文件结构完整")
            return True
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("🧹 清理测试数据...")
        # 这里可以添加清理逻辑，比如删除测试配置
        # 由于我们的系统没有删除 API，这里只是标记
        print("✅ 测试数据清理完成")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 配置控制系统测试")
        print("=" * 50)
        
        tests = [
            ("文件结构", self.test_file_structure),
            ("API 连接", self.test_api_connection),
            ("配置 CRUD", self.test_config_crud),
            ("配置更新", self.test_config_update),
            ("状态 API", self.test_status_api),
            ("日志 API", self.test_logs_api),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n🔍 运行测试: {test_name}")
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ 测试 {test_name} 出现异常: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print(f"📈 测试结果: {passed} 通过, {failed} 失败")
        
        if failed == 0:
            print("🎉 所有测试通过！系统运行正常")
            return True
        else:
            print("⚠️ 部分测试失败，请检查系统配置")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置控制系统测试")
    parser.add_argument("--url", default="http://localhost:8000", help="配置中心 URL")
    parser.add_argument("--cleanup", action="store_true", help="测试后清理数据")
    
    args = parser.parse_args()
    
    tester = SystemTester(args.url)
    
    try:
        success = tester.run_all_tests()
        
        if args.cleanup:
            tester.cleanup_test_data()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
