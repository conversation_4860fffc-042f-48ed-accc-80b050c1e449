"""
初始化数据脚本
为测试创建一些示例配置数据
"""

import requests
import json

def init_sample_data():
    """初始化示例配置数据"""
    base_url = "http://localhost:8000"
    
    # 示例配置数据
    sample_configs = [
        {
            "app": "test_app",
            "configs": {
                "feature_x_enabled": True,
                "max_threads": 5,
                "logging_level": "debug",
                "api_timeout": 30.0,
                "allowed_ips": ["127.0.0.1", "***********/24"],
                "database_config": {
                    "host": "localhost",
                    "port": 5432,
                    "name": "testdb"
                }
            }
        },
        {
            "app": "production_app",
            "configs": {
                "feature_x_enabled": False,
                "max_threads": 10,
                "logging_level": "info",
                "api_timeout": 60.0,
                "allowed_ips": ["0.0.0.0/0"],
                "database_config": {
                    "host": "prod-db.example.com",
                    "port": 5432,
                    "name": "proddb"
                }
            }
        }
    ]
    
    print("正在初始化示例配置数据...")
    
    for config_data in sample_configs:
        try:
            response = requests.post(
                f"{base_url}/config/update",
                json=config_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                print(f"✓ 成功创建 {config_data['app']} 的配置")
            else:
                print(f"✗ 创建 {config_data['app']} 配置失败: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("✗ 无法连接到配置控制中心，请确保服务已启动 (python main.py)")
            return False
        except Exception as e:
            print(f"✗ 创建配置时出错: {e}")
            return False
    
    print("\n示例数据初始化完成！")
    print("您可以通过以下方式测试:")
    print("1. 访问 http://localhost:8000/config?app=test_app")
    print("2. 访问 http://localhost:8000/config/status")
    print("3. 访问 http://localhost:8000/docs 查看 API 文档")
    
    return True

if __name__ == "__main__":
    init_sample_data()
