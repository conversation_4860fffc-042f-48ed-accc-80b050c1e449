#!/usr/bin/env python3
"""
一键启动脚本
启动配置控制中心、Agent 和 Web 客户端
"""

import subprocess
import sys
import time
import os
import signal
import threading
from pathlib import Path

class ProcessManager:
    def __init__(self):
        self.processes = []
        self.running = True
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        print("\n收到退出信号，正在停止所有服务...")
        self.stop_all()
        sys.exit(0)
    
    def start_controller(self):
        """启动配置控制中心"""
        print("🚀 启动配置控制中心...")
        controller_dir = Path("controller")
        
        if not controller_dir.exists():
            print("❌ controller 目录不存在")
            return None
        
        try:
            process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=controller_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            self.processes.append(("配置控制中心", process))
            print("✅ 配置控制中心启动成功 (PID: {})".format(process.pid))
            return process
        except Exception as e:
            print(f"❌ 启动配置控制中心失败: {e}")
            return None
    
    def start_agent(self, app_name="test_app"):
        """启动 Agent"""
        print(f"🤖 启动 Agent ({app_name})...")
        agent_dir = Path("agent")
        
        if not agent_dir.exists():
            print("❌ agent 目录不存在")
            return None
        
        try:
            process = subprocess.Popen(
                [sys.executable, "main.py", "--app", app_name],
                cwd=agent_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            self.processes.append((f"Agent-{app_name}", process))
            print(f"✅ Agent ({app_name}) 启动成功 (PID: {process.pid})")
            return process
        except Exception as e:
            print(f"❌ 启动 Agent 失败: {e}")
            return None
    
    def start_web_client(self):
        """启动 Web 客户端"""
        print("🌐 启动 Web 客户端...")
        web_dir = Path("web-client")
        
        if not web_dir.exists():
            print("❌ web-client 目录不存在")
            return None
        
        # 检查是否已安装依赖
        node_modules = web_dir / "node_modules"
        if not node_modules.exists():
            print("📦 正在安装 Web 客户端依赖...")
            try:
                subprocess.run(["npm", "install"], cwd=web_dir, check=True)
                print("✅ 依赖安装完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装依赖失败: {e}")
                return None
            except FileNotFoundError:
                print("❌ 未找到 npm 命令，请确保已安装 Node.js")
                return None
        
        try:
            process = subprocess.Popen(
                ["npm", "start"],
                cwd=web_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            self.processes.append(("Web客户端", process))
            print("✅ Web 客户端启动成功 (PID: {})".format(process.pid))
            return process
        except Exception as e:
            print(f"❌ 启动 Web 客户端失败: {e}")
            return None
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 检查 Python 依赖
        controller_deps = Path("controller/requirements.txt")
        agent_deps = Path("agent/requirements.txt")
        
        if controller_deps.exists():
            print("📋 检查配置控制中心依赖...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(controller_deps)
                ], check=True, capture_output=True)
                print("✅ 配置控制中心依赖已安装")
            except subprocess.CalledProcessError:
                print("⚠️ 配置控制中心依赖安装可能有问题")
        
        if agent_deps.exists():
            print("📋 检查 Agent 依赖...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(agent_deps)
                ], check=True, capture_output=True)
                print("✅ Agent 依赖已安装")
            except subprocess.CalledProcessError:
                print("⚠️ Agent 依赖安装可能有问题")
    
    def wait_for_service(self, url, timeout=30):
        """等待服务启动"""
        import requests
        
        for i in range(timeout):
            try:
                response = requests.get(url, timeout=1)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False
    
    def init_sample_data(self):
        """初始化示例数据"""
        print("📊 初始化示例数据...")
        controller_dir = Path("controller")
        
        if not controller_dir.exists():
            print("❌ controller 目录不存在")
            return
        
        try:
            subprocess.run([
                sys.executable, "init_data.py"
            ], cwd=controller_dir, check=True, capture_output=True)
            print("✅ 示例数据初始化完成")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 示例数据初始化失败: {e}")
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)
            for name, process in self.processes:
                if process.poll() is not None:
                    print(f"⚠️ {name} 进程已退出 (返回码: {process.returncode})")
                    # 可以在这里添加重启逻辑
            
    def stop_all(self):
        """停止所有进程"""
        self.running = False
        
        for name, process in self.processes:
            if process.poll() is None:
                print(f"🛑 停止 {name}...")
                process.terminate()
                
                # 等待进程优雅退出
                try:
                    process.wait(timeout=5)
                    print(f"✅ {name} 已停止")
                except subprocess.TimeoutExpired:
                    print(f"⚠️ {name} 未能优雅退出，强制终止...")
                    process.kill()
                    process.wait()
                    print(f"✅ {name} 已强制终止")
    
    def start_all(self):
        """启动所有服务"""
        print("🎯 配置控制系统一键启动")
        print("=" * 50)
        
        # 检查依赖
        self.check_dependencies()
        print()
        
        # 启动配置控制中心
        controller_process = self.start_controller()
        if not controller_process:
            print("❌ 无法启动配置控制中心，退出")
            return
        
        # 等待配置控制中心启动
        print("⏳ 等待配置控制中心启动...")
        if self.wait_for_service("http://localhost:8000"):
            print("✅ 配置控制中心已就绪")
        else:
            print("⚠️ 配置控制中心可能未完全启动")
        
        # 初始化示例数据
        time.sleep(2)
        self.init_sample_data()
        print()
        
        # 启动 Agent
        agent_process = self.start_agent("test_app")
        time.sleep(2)
        
        # 启动 Web 客户端
        web_process = self.start_web_client()
        print()
        
        # 显示访问信息
        print("🎉 所有服务启动完成！")
        print("=" * 50)
        print("📍 访问地址:")
        print("  • 配置控制中心: http://localhost:8000")
        print("  • API 文档: http://localhost:8000/docs")
        print("  • Web 客户端: http://localhost:3000")
        print()
        print("💡 使用说明:")
        print("  • 在 Web 界面中可以查看和修改配置")
        print("  • Agent 会每 10 秒自动拉取最新配置")
        print("  • 按 Ctrl+C 可以停止所有服务")
        print()
        
        # 启动进程监控
        monitor_thread = threading.Thread(target=self.monitor_processes)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 等待用户中断
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

def main():
    manager = ProcessManager()
    try:
        manager.start_all()
    except KeyboardInterrupt:
        pass
    finally:
        manager.stop_all()

if __name__ == "__main__":
    main()
