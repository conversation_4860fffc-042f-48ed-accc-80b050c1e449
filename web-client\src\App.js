import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Form,
  Input,
  Button,
  Select,
  Switch,
  InputNumber,
  message,
  Tabs,
  Table,
  Tag,
  Space,
  Divider,
  Typography,
  Alert,
  Spin
} from 'antd';
import {
  SettingOutlined,
  ReloadOutlined,
  SaveOutlined,
  EyeOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import axios from 'axios';
import './index.css';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 配置 axios 基础 URL
axios.defaults.baseURL = 'http://localhost:8000';

function App() {
  const [loading, setLoading] = useState(false);
  const [currentApp, setCurrentApp] = useState('test_app');
  const [config, setConfig] = useState({});
  const [allStatus, setAllStatus] = useState({});
  const [logs, setLogs] = useState([]);
  const [form] = Form.useForm();

  // 获取配置
  const fetchConfig = async (appName = currentApp) => {
    setLoading(true);
    try {
      const response = await axios.get(`/config?app=${appName}`);
      const configData = response.data.configs || {};
      setConfig(configData);
      
      // 更新表单值
      form.setFieldsValue(configData);
      
      message.success(`成功获取 ${appName} 的配置`);
    } catch (error) {
      message.error(`获取配置失败: ${error.message}`);
      console.error('获取配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取所有应用状态
  const fetchAllStatus = async () => {
    try {
      const response = await axios.get('/config/status');
      setAllStatus(response.data.apps || {});
    } catch (error) {
      message.error(`获取状态失败: ${error.message}`);
      console.error('获取状态失败:', error);
    }
  };

  // 获取配置日志
  const fetchLogs = async (appName = null) => {
    try {
      const params = appName ? { app_name: appName } : {};
      const response = await axios.get('/config/logs', { params });
      setLogs(response.data.logs || []);
    } catch (error) {
      message.error(`获取日志失败: ${error.message}`);
      console.error('获取日志失败:', error);
    }
  };

  // 更新配置
  const updateConfig = async (values) => {
    setLoading(true);
    try {
      const response = await axios.post('/config/update', {
        app: currentApp,
        configs: values
      });
      
      message.success('配置更新成功');
      
      // 重新获取配置
      await fetchConfig();
      await fetchAllStatus();
      await fetchLogs();
      
    } catch (error) {
      message.error(`更新配置失败: ${error.message}`);
      console.error('更新配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchConfig();
    fetchAllStatus();
    fetchLogs();
  }, [currentApp]);

  // 渲染配置表单
  const renderConfigForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={updateConfig}
        className="config-form"
      >
        <Form.Item
          name="feature_x_enabled"
          label="功能 X 启用状态"
          valuePropName="checked"
        >
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>

        <Form.Item
          name="max_threads"
          label="最大线程数"
          rules={[{ type: 'number', min: 1, max: 100 }]}
        >
          <InputNumber min={1} max={100} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="logging_level"
          label="日志级别"
        >
          <Select>
            <Option value="debug">DEBUG</Option>
            <Option value="info">INFO</Option>
            <Option value="warning">WARNING</Option>
            <Option value="error">ERROR</Option>
            <Option value="critical">CRITICAL</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="api_timeout"
          label="API 超时时间 (秒)"
          rules={[{ type: 'number', min: 1, max: 300 }]}
        >
          <InputNumber min={1} max={300} step={0.1} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="allowed_ips"
          label="允许的 IP 地址 (JSON 数组)"
        >
          <TextArea
            rows={3}
            placeholder='["127.0.0.1", "***********/24"]'
          />
        </Form.Item>

        <Form.Item
          name="database_config"
          label="数据库配置 (JSON 对象)"
        >
          <TextArea
            rows={4}
            placeholder='{"host": "localhost", "port": 5432, "name": "testdb"}'
          />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
            >
              保存配置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchConfig()}
              loading={loading}
            >
              刷新配置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 渲染当前配置显示
  const renderCurrentConfig = () => {
    return (
      <Card title="当前配置" className="config-card">
        <div className="json-display">
          {JSON.stringify(config, null, 2)}
        </div>
      </Card>
    );
  };

  // 渲染所有应用状态
  const renderAllStatus = () => {
    const columns = [
      {
        title: '应用名称',
        dataIndex: 'app_name',
        key: 'app_name',
        render: (text) => <Tag color="blue">{text}</Tag>
      },
      {
        title: '配置项数量',
        dataIndex: 'config_count',
        key: 'config_count'
      },
      {
        title: '最后更新时间',
        dataIndex: 'last_updated',
        key: 'last_updated',
        render: (text) => text ? new Date(text).toLocaleString() : '-'
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record) => (
          <Button
            size="small"
            onClick={() => setCurrentApp(record.app_name)}
          >
            查看配置
          </Button>
        )
      }
    ];

    const dataSource = Object.entries(allStatus).map(([appName, status]) => ({
      key: appName,
      app_name: appName,
      config_count: Object.keys(status.configs || {}).length,
      last_updated: status.last_updated
    }));

    return (
      <Table
        columns={columns}
        dataSource={dataSource}
        size="small"
        pagination={false}
      />
    );
  };

  // 渲染配置日志
  const renderLogs = () => {
    const columns = [
      {
        title: '应用',
        dataIndex: 'app_name',
        key: 'app_name',
        render: (text) => <Tag>{text}</Tag>
      },
      {
        title: '配置项',
        dataIndex: 'config_key',
        key: 'config_key'
      },
      {
        title: '旧值',
        dataIndex: 'old_value',
        key: 'old_value',
        render: (text) => text || <Text type="secondary">新增</Text>
      },
      {
        title: '新值',
        dataIndex: 'new_value',
        key: 'new_value'
      },
      {
        title: '变更时间',
        dataIndex: 'changed_at',
        key: 'changed_at',
        render: (text) => new Date(text).toLocaleString()
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={logs}
        size="small"
        pagination={{ pageSize: 10 }}
        rowKey="id"
      />
    );
  };

  const tabItems = [
    {
      key: '1',
      label: (
        <span>
          <SettingOutlined />
          配置管理
        </span>
      ),
      children: (
        <div>
          <Card
            title={`应用配置 - ${currentApp}`}
            extra={
              <Select
                value={currentApp}
                onChange={setCurrentApp}
                style={{ width: 200 }}
              >
                <Option value="test_app">测试应用</Option>
                <Option value="production_app">生产应用</Option>
                <Option value="custom_app">自定义应用</Option>
              </Select>
            }
            className="config-card"
          >
            {renderConfigForm()}
          </Card>
          {renderCurrentConfig()}
        </div>
      )
    },
    {
      key: '2',
      label: (
        <span>
          <EyeOutlined />
          状态监控
        </span>
      ),
      children: (
        <Card
          title="所有应用状态"
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAllStatus}
            >
              刷新状态
            </Button>
          }
        >
          {renderAllStatus()}
        </Card>
      )
    },
    {
      key: '3',
      label: (
        <span>
          <HistoryOutlined />
          变更日志
        </span>
      ),
      children: (
        <Card
          title="配置变更日志"
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchLogs()}
            >
              刷新日志
            </Button>
          }
        >
          {renderLogs()}
        </Card>
      )
    }
  ];

  return (
    <Layout className="app-container">
      <Header style={{ background: '#fff', padding: '0 20px' }}>
        <Title level={2} style={{ margin: 0, lineHeight: '64px' }}>
          配置控制中心
        </Title>
      </Header>
      
      <Content style={{ padding: '20px' }}>
        <Alert
          message="系统说明"
          description="这是一个本地配置控制系统，可以管理多个应用的配置参数。修改配置后，Agent 程序会自动获取最新配置并执行相应操作。"
          type="info"
          showIcon
          style={{ marginBottom: 20 }}
        />
        
        <Spin spinning={loading}>
          <Tabs items={tabItems} />
        </Spin>
      </Content>
    </Layout>
  );
}

export default App;
