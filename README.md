# 配置控制系统

一个完整的本地配置控制系统，包含配置控制中心（FastAPI）、Python 客户端 Agent 和 React Web 客户端。

## 项目结构

```
project_root/
├── controller/       # FastAPI 配置控制中心服务端
│   ├── main.py      # 主服务文件
│   ├── init_data.py # 初始化示例数据
│   └── requirements.txt
├── agent/           # Python Agent 客户端
│   ├── main.py      # Agent 主程序
│   ├── example_usage.py # 使用示例
│   └── requirements.txt
├── web-client/      # React 前端客户端
│   ├── src/         # 源代码
│   ├── public/      # 静态文件
│   └── package.json
└── README.md        # 项目说明
```

## 功能特性

### 配置控制中心 (FastAPI)
- ✅ RESTful API 接口
- ✅ SQLite 数据库存储
- ✅ 跨域支持 (CORS)
- ✅ 配置版本日志
- ✅ 自动 API 文档

### Python 客户端 Agent
- ✅ 定时拉取配置 (每10秒)
- ✅ 配置变化检测
- ✅ 功能开关控制
- ✅ 日志级别动态调整
- ✅ 优雅退出处理

### React Web 客户端
- ✅ 配置查看和修改
- ✅ 多应用管理
- ✅ 状态监控
- ✅ 变更日志查看
- ✅ 美观的用户界面

## 快速开始

### 1. 启动配置控制中心

```bash
cd controller
pip install -r requirements.txt
python main.py
```

服务将在 http://localhost:8000 启动

### 2. 初始化示例数据 (可选)

```bash
cd controller
python init_data.py
```

### 3. 启动 Python Agent

```bash
cd agent
pip install -r requirements.txt
python main.py --app test_app
```

### 4. 启动 Web 客户端

```bash
cd web-client
npm install
npm start
```

Web 界面将在 http://localhost:3000 启动

## API 接口

### 获取配置
```http
GET /config?app=<app_name>
```

### 更新配置
```http
POST /config/update
Content-Type: application/json

{
  "app": "test_app",
  "configs": {
    "feature_x_enabled": true,
    "max_threads": 5,
    "logging_level": "debug"
  }
}
```

### 获取所有应用状态
```http
GET /config/status
```

### 获取配置变更日志
```http
GET /config/logs?app_name=<app_name>&limit=50
```

## 使用示例

### Agent 命令行参数

```bash
# 指定应用名称
python main.py --app production_app

# 指定配置中心地址
python main.py --app test_app --url http://localhost:8000

# 指定拉取间隔（秒）
python main.py --app test_app --interval 5
```

### 配置示例

```json
{
  "feature_x_enabled": true,
  "max_threads": 5,
  "logging_level": "debug",
  "api_timeout": 30.0,
  "allowed_ips": ["127.0.0.1", "***********/24"],
  "database_config": {
    "host": "localhost",
    "port": 5432,
    "name": "testdb"
  }
}
```

## 开发和测试

### 测试 API 接口

访问 http://localhost:8000/docs 查看自动生成的 API 文档

### 测试 Agent

```bash
cd agent
python example_usage.py
```

### 构建 Web 客户端

```bash
cd web-client
npm run build
```

## 配置说明

### 支持的配置类型

- **布尔值**: `true`/`false`
- **整数**: `5`, `100`
- **浮点数**: `30.5`, `1.0`
- **字符串**: `"debug"`, `"info"`
- **JSON 数组**: `["127.0.0.1", "***********"]`
- **JSON 对象**: `{"host": "localhost", "port": 5432}`

### Agent 功能映射

- `feature_x_enabled`: 控制功能 X 的启用/禁用
- `max_threads`: 设置最大线程数
- `logging_level`: 动态调整日志级别
- `api_timeout`: 设置 API 超时时间

## 故障排除

### 常见问题

1. **无法连接到配置中心**
   - 确保配置中心服务已启动
   - 检查端口 8000 是否被占用
   - 确认防火墙设置

2. **Web 客户端无法访问 API**
   - 检查 CORS 配置
   - 确认代理设置 (package.json 中的 proxy)

3. **Agent 无法获取配置**
   - 检查网络连接
   - 确认应用名称是否正确
   - 查看 Agent 日志输出

### 日志查看

- **配置中心**: 控制台输出
- **Agent**: 控制台输出，可通过 `logging_level` 配置调整
- **Web 客户端**: 浏览器开发者工具

## 扩展开发

### 添加新的配置项

1. 在 Agent 的 `_execute_config_action` 方法中添加处理逻辑
2. 在 Web 客户端的表单中添加对应的输入组件
3. 更新配置示例和文档

### 添加新的 API 接口

1. 在 `controller/main.py` 中添加新的路由
2. 更新 API 文档
3. 在 Web 客户端中添加对应的调用

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
