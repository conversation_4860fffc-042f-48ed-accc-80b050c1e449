"""
Python 客户端 Agent
定时从配置控制中心拉取配置并执行相应功能
"""

import requests
import json
import time
import logging
import threading
from datetime import datetime
from typing import Dict, Any, Optional
import signal
import sys

class ConfigAgent:
    def __init__(self, app_name: str, config_center_url: str = "http://localhost:8000"):
        self.app_name = app_name
        self.config_center_url = config_center_url
        self.current_config = {}
        self.last_updated = None
        self.running = False
        self.fetch_interval = 10  # 10秒拉取一次配置
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(f"ConfigAgent-{app_name}")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        self.logger.info("收到退出信号，正在停止 Agent...")
        self.stop()
        sys.exit(0)
    
    def fetch_config(self) -> Optional[Dict[str, Any]]:
        """从配置中心获取配置"""
        try:
            response = requests.get(
                f"{self.config_center_url}/config",
                params={"app": self.app_name},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("configs", {})
            else:
                self.logger.error(f"获取配置失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到配置控制中心")
            return None
        except requests.exceptions.Timeout:
            self.logger.error("请求配置中心超时")
            return None
        except Exception as e:
            self.logger.error(f"获取配置时出错: {e}")
            return None
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新本地配置"""
        if new_config != self.current_config:
            old_config = self.current_config.copy()
            self.current_config = new_config
            self.last_updated = datetime.now()
            
            self.logger.info("配置已更新:")
            self.logger.info(f"当前配置: {json.dumps(new_config, indent=2, ensure_ascii=False)}")
            
            # 检查配置变化
            self._handle_config_changes(old_config, new_config)
            return True
        
        return False
    
    def _handle_config_changes(self, old_config: Dict[str, Any], new_config: Dict[str, Any]):
        """处理配置变化"""
        for key, new_value in new_config.items():
            old_value = old_config.get(key)
            if old_value != new_value:
                self.logger.info(f"配置项 '{key}' 从 '{old_value}' 变更为 '{new_value}'")
                
                # 根据配置项执行相应操作
                self._execute_config_action(key, new_value, old_value)
    
    def _execute_config_action(self, key: str, new_value: Any, old_value: Any):
        """根据配置执行相应操作"""
        if key == "feature_x_enabled":
            if new_value:
                self.logger.info("🟢 功能 X 已启用")
                self.enable_feature_x()
            else:
                self.logger.info("🔴 功能 X 已禁用")
                self.disable_feature_x()
        
        elif key == "max_threads":
            self.logger.info(f"🔧 最大线程数设置为: {new_value}")
            self.set_max_threads(new_value)
        
        elif key == "logging_level":
            self.logger.info(f"📝 日志级别设置为: {new_value}")
            self.set_logging_level(new_value)
        
        elif key == "api_timeout":
            self.logger.info(f"⏱️ API 超时时间设置为: {new_value}秒")
            self.set_api_timeout(new_value)
    
    def enable_feature_x(self):
        """启用功能 X"""
        self.logger.info("执行功能 X 的启用逻辑...")
        # 这里可以添加具体的功能启用代码
        # 例如：启动某个服务、开启某个功能模块等
        
    def disable_feature_x(self):
        """禁用功能 X"""
        self.logger.info("执行功能 X 的禁用逻辑...")
        # 这里可以添加具体的功能禁用代码
        
    def set_max_threads(self, max_threads: int):
        """设置最大线程数"""
        self.logger.info(f"设置线程池最大线程数为: {max_threads}")
        # 这里可以添加线程池配置代码
        
    def set_logging_level(self, level: str):
        """设置日志级别"""
        level_map = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "critical": logging.CRITICAL
        }
        
        if level.lower() in level_map:
            logging.getLogger().setLevel(level_map[level.lower()])
            self.logger.info(f"日志级别已设置为: {level.upper()}")
        else:
            self.logger.warning(f"未知的日志级别: {level}")
    
    def set_api_timeout(self, timeout: float):
        """设置 API 超时时间"""
        self.logger.info(f"API 超时时间设置为: {timeout}秒")
        # 这里可以添加 API 客户端超时配置代码
    
    def run_periodic_task(self):
        """执行周期性任务"""
        # 根据当前配置执行一些周期性任务
        if self.current_config.get("feature_x_enabled", False):
            self.logger.debug("执行功能 X 的周期性任务...")
            # 这里可以添加具体的周期性任务代码
        
        # 可以根据其他配置项执行不同的任务
        max_threads = self.current_config.get("max_threads", 1)
        self.logger.debug(f"当前配置允许最大 {max_threads} 个线程")
    
    def start(self):
        """启动 Agent"""
        self.running = True
        self.logger.info(f"配置 Agent 启动，应用名称: {self.app_name}")
        self.logger.info(f"配置中心地址: {self.config_center_url}")
        self.logger.info(f"拉取间隔: {self.fetch_interval}秒")
        
        # 首次获取配置
        initial_config = self.fetch_config()
        if initial_config:
            self.update_config(initial_config)
        else:
            self.logger.warning("无法获取初始配置，使用默认配置")
        
        # 启动配置拉取循环
        while self.running:
            try:
                time.sleep(self.fetch_interval)
                
                if not self.running:
                    break
                
                # 获取最新配置
                new_config = self.fetch_config()
                if new_config:
                    self.update_config(new_config)
                
                # 执行周期性任务
                self.run_periodic_task()
                
            except KeyboardInterrupt:
                self.logger.info("收到中断信号，正在停止...")
                break
            except Exception as e:
                self.logger.error(f"运行时出错: {e}")
                time.sleep(5)  # 出错后等待5秒再继续
    
    def stop(self):
        """停止 Agent"""
        self.running = False
        self.logger.info("配置 Agent 已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取 Agent 状态"""
        return {
            "app_name": self.app_name,
            "running": self.running,
            "current_config": self.current_config,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
            "config_center_url": self.config_center_url
        }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置 Agent")
    parser.add_argument("--app", default="test_app", help="应用名称")
    parser.add_argument("--url", default="http://localhost:8000", help="配置中心 URL")
    parser.add_argument("--interval", type=int, default=10, help="配置拉取间隔（秒）")
    
    args = parser.parse_args()
    
    # 创建并启动 Agent
    agent = ConfigAgent(args.app, args.url)
    agent.fetch_interval = args.interval
    
    try:
        agent.start()
    except KeyboardInterrupt:
        agent.stop()

if __name__ == "__main__":
    main()
