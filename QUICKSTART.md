# 快速启动指南

## 🚀 一键启动（推荐）

```bash
python start_all.py
```

这个脚本会自动：
- 安装所有依赖
- 启动配置控制中心
- 初始化示例数据
- 启动 Python Agent
- 启动 Web 客户端

## 📋 手动启动步骤

### 1. 启动配置控制中心

```bash
cd controller
pip install -r requirements.txt
python main.py
```

服务地址: http://localhost:8000

### 2. 初始化示例数据（可选）

```bash
cd controller
python init_data.py
```

### 3. 启动 Python Agent

```bash
cd agent
pip install -r requirements.txt
python main.py --app test_app
```

### 4. 启动 Web 客户端

```bash
cd web-client
npm install
npm start
```

Web 地址: http://localhost:3000

## 🧪 测试系统

```bash
python test_system.py
```

## 📱 使用示例

### 通过 API 操作配置

```bash
# 获取配置
curl "http://localhost:8000/config?app=test_app"

# 更新配置
curl -X POST "http://localhost:8000/config/update" \
  -H "Content-Type: application/json" \
  -d '{
    "app": "test_app",
    "configs": {
      "feature_x_enabled": false,
      "max_threads": 8
    }
  }'

# 查看所有应用状态
curl "http://localhost:8000/config/status"
```

### 通过 Web 界面

1. 打开 http://localhost:3000
2. 选择应用名称
3. 修改配置参数
4. 点击"保存配置"
5. 查看 Agent 控制台输出的变化

## 🔧 常见问题

### 端口被占用

如果 8000 或 3000 端口被占用：

```bash
# 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 修改端口（在相应的配置文件中）
# controller/main.py: uvicorn.run(app, host="0.0.0.0", port=8001)
# web-client/package.json: "proxy": "http://localhost:8001"
```

### 依赖安装失败

```bash
# 升级 pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# Node.js 依赖
npm install --registry https://registry.npm.taobao.org
```

### Agent 无法连接

1. 确保配置控制中心已启动
2. 检查防火墙设置
3. 确认 URL 配置正确

## 📊 监控和日志

- **配置中心日志**: 控制台输出
- **Agent 日志**: 控制台输出，可通过配置调整级别
- **Web 客户端**: 浏览器开发者工具
- **API 文档**: http://localhost:8000/docs

## 🎯 下一步

1. 根据需要修改配置项
2. 扩展 Agent 的功能逻辑
3. 自定义 Web 界面
4. 添加更多应用配置

## 💡 提示

- 修改配置后，Agent 会在 10 秒内自动获取更新
- 所有配置变更都会记录在日志中
- 支持多种数据类型：布尔值、数字、字符串、JSON 对象/数组
